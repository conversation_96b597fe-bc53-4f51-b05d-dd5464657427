# CSV/Excel Splitter & Merger Pro Launcher
Write-Host "Starting CSV/Excel Splitter & Merger Pro..." -ForegroundColor Green
Write-Host "Location: $PWD" -ForegroundColor Yellow
Write-Host ""

# Check if virtual environment exists
if (Test-Path ".\.venv\Scripts\Activate.ps1") {
    Write-Host "Activating virtual environment..." -ForegroundColor Cyan
    & ".\.venv\Scripts\Activate.ps1"

    Write-Host "Running application with virtual environment..." -ForegroundColor Green
    try {
        python split_csv_gui_frontend.py
        Write-Host ""
        Write-Host "Application closed successfully." -ForegroundColor Green
    } catch {
        Write-Host "Error running application: $_" -ForegroundColor Red
    }
} else {
    Write-Host "Virtual environment not found, using system Python..." -ForegroundColor Yellow
    try {
        & "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe" split_csv_gui_frontend.py
        Write-Host ""
        Write-Host "Application closed successfully." -ForegroundColor Green
    } catch {
        Write-Host "Error running application: $_" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
