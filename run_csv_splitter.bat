@echo off
echo Starting CSV/Excel Splitter & Merger Pro...
echo.

REM Check if virtual environment exists
if exist ".venv\Scripts\activate.bat" (
    echo Activating virtual environment...
    call .venv\Scripts\activate.bat
    echo Running application with virtual environment...
    python split_csv_gui_frontend.py
) else (
    echo Virtual environment not found, using system Python...
    "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe" split_csv_gui_frontend.py
)

echo.
echo Application closed.
pause
