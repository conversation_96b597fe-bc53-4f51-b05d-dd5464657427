# Setup script for CSV/Excel Splitter & Merger Pro
Write-Host "Setting up Python environment..." -ForegroundColor Green
Write-Host ""

# Remove old virtual environment if it exists
if (Test-Path ".venv") {
    Write-Host "Removing old virtual environment..." -ForegroundColor Yellow
    Remove-Item -Recurse -Force .venv
}

# Create new virtual environment with correct Python version
Write-Host "Creating new virtual environment with Python 3.13..." -ForegroundColor Cyan
& "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe" -m venv .venv

# Activate virtual environment
Write-Host "Activating virtual environment..." -ForegroundColor Cyan
& ".\.venv\Scripts\Activate.ps1"

# Install required packages
Write-Host "Installing required packages (pandas, openpyxl)..." -ForegroundColor Cyan
python -m pip install --upgrade pip
python -m pip install pandas openpyxl

# Test installation
Write-Host "Testing installation..." -ForegroundColor Cyan
python -c "import pandas; import openpyxl; print('✅ All packages installed successfully!')"

Write-Host ""
Write-Host "🎉 Setup complete! You can now run your application with:" -ForegroundColor Green
Write-Host "   .\run_csv_splitter.bat" -ForegroundColor Yellow
Write-Host "   or" -ForegroundColor Yellow
Write-Host "   python split_csv_gui_frontend.py" -ForegroundColor Yellow
Write-Host ""
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
