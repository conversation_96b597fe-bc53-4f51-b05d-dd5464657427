#!/usr/bin/env python3
"""
Split or merge CSV / Excel files via a tiny Tk UI.

• Split mode  – choose one file → pick an output folder → enter chunk size.
• Merge mode – pick one or many files → choose where to save combined file.

Dependencies:
    pip install pandas openpyxl
"""

import os
import tkinter as tk
from tkinter import filedialog
import pandas as pd
from typing import List


# ───────────────────────── File/Folder pickers ──────────────────────────
def select_files() -> List[str]:
    """
    Ask for multiple CSV / XLSX files.

    Returns a real list on every platform (macOS sometimes returns a single
    space-separated string, which we split with `root.tk.splitlist`).
    """
    root = tk.Tk()
    root.withdraw()

    paths = filedialog.askopenfilenames(
        parent=root,
        title="Select the files to merge",
        filetypes=[
            ("Excel & CSV files", ("*.xlsx", "*.csv")),
            ("Excel files", "*.xlsx"),
            ("CSV files", "*.csv"),
        ],
    )

    # macOS quirk: askopen<PERSON>lenames may return one big string
    if isinstance(paths, str):
        paths = root.tk.splitlist(paths)

    root.destroy()
    return list(paths)          # always a list (possibly empty)

def select_file() -> str:
    """Single CSV / XLSX file."""
    root = tk.Tk()
    root.withdraw()
    path = filedialog.askopenfilename(
        parent=root,
        title="Select a file",
        filetypes=[
            ("Excel & CSV files", ("*.xlsx", "*.csv")),
            ("Excel files", "*.xlsx"),
            ("CSV files", "*.csv"),
        ],
    )
    root.destroy()
    return path

def select_directory() -> str:
    """Folder picker."""
    root = tk.Tk()
    root.withdraw()
    folder = filedialog.askdirectory(parent=root, title="Choose output folder")
    root.destroy()
    return folder

# ───────────────────────── Core functionality ───────────────────────────
def split_file(input_file: str, output_dir: str, chunk_size: int) -> None:
    # ... (existing split_file code unchanged)
    try:
        ext = os.path.splitext(input_file)[1].lower()
        if ext == ".csv":
            # Try multiple strategies to read problematic CSV files while preserving column structure
            df = None
            parsing_method = "unknown"

            try:
                # First attempt: standard reading
                df = pd.read_csv(input_file, dtype=str)
                parsing_method = "standard pandas"
                print("✓ Successfully parsed using standard pandas CSV reader")

            except pd.errors.ParserError as e:
                print(f"Standard CSV parsing failed: {e}")
                print("🔍 Scanning file for ALL problematic lines...")

                # Comprehensive bad line detection
                bad_lines = []
                expected_column_count = None

                try:
                    # Get expected column count from header
                    with open(input_file, 'r', encoding='utf-8') as f:
                        first_line = f.readline().strip()
                        expected_column_count = len(first_line.split(','))
                    print(f"Expected columns based on header: {expected_column_count}")

                    # Scan entire file for problematic lines
                    with open(input_file, 'r', encoding='utf-8') as f:
                        for line_num, line in enumerate(f, 1):
                            if line_num == 1:  # Skip header
                                continue

                            # Count columns in this line (simple comma count)
                            actual_columns = len(line.strip().split(','))
                            if actual_columns != expected_column_count:
                                bad_lines.append({
                                    'line_num': line_num,
                                    'expected': expected_column_count,
                                    'found': actual_columns,
                                    'content': line.strip()[:100] + '...' if len(line.strip()) > 100 else line.strip()
                                })

                            # Limit scan to avoid overwhelming output
                            if len(bad_lines) >= 20:
                                print(f"⚠ Found 20+ bad lines, stopping scan...")
                                break

                    if bad_lines:
                        print(f"🚨 Found {len(bad_lines)} problematic lines:")
                        for i, bad_line in enumerate(bad_lines[:10]):  # Show first 10
                            print(f"   Line {bad_line['line_num']}: {bad_line['expected']} → {bad_line['found']} columns")
                            print(f"      Content: {bad_line['content']}")

                        if len(bad_lines) > 10:
                            print(f"   ... and {len(bad_lines) - 10} more problematic lines")

                        print(f"\n🔧 QUICK FIX SUGGESTIONS:")
                        print(f"   1. AUTOMATIC FIX: Use 'Skip bad lines' mode (recommended)")
                        print(f"   2. MANUAL FIX: Open CSV in text editor and check lines: {[bl['line_num'] for bl in bad_lines[:5]]}")
                        print(f"   3. EXCEL FIX: Open in Excel, save as CSV again")
                        print(f"   4. COMMON ISSUES:")
                        print(f"      • Unescaped commas in text fields")
                        print(f"      • Missing quotes around fields containing commas")
                        print(f"      • Extra commas at end of lines")
                        print(f"      • Line breaks within quoted fields")

                except Exception as scan_error:
                    print(f"Could not scan file: {scan_error}")

                print("Trying alternative parsing methods...")

                try:
                    # Second attempt: use python engine (more forgiving but preserves structure)
                    df = pd.read_csv(input_file, dtype=str, engine='python')
                    parsing_method = "python engine"
                    print("✓ Successfully parsed using Python engine")

                except Exception as e2:
                    print(f"Python engine failed: {e2}")

                    try:
                        # Third attempt: skip bad lines but SAVE them to separate file
                        # First, try to read just the header to get column names
                        header_df = pd.read_csv(input_file, dtype=str, nrows=1)
                        expected_columns = list(header_df.columns)
                        print(f"Expected columns from header: {expected_columns}")

                        # Save bad lines to separate error file
                        if bad_lines:
                            error_file_path = os.path.join(
                                output_dir,
                                f"{os.path.splitext(os.path.basename(input_file))[0]}_ERRORS.csv"
                            )

                            print(f"💾 Saving {len(bad_lines)} problematic lines to: {error_file_path}")

                            # Create error file with bad line data
                            error_data = []
                            for bad_line in bad_lines:
                                # Try to parse the bad line as best as possible
                                line_parts = bad_line['content'].split(',')

                                # Pad or truncate to match expected columns
                                if len(line_parts) < len(expected_columns):
                                    # Add empty fields for missing columns
                                    line_parts.extend([''] * (len(expected_columns) - len(line_parts)))
                                elif len(line_parts) > len(expected_columns):
                                    # Combine extra fields into the last column
                                    extra_data = ','.join(line_parts[len(expected_columns)-1:])
                                    line_parts = line_parts[:len(expected_columns)-1] + [extra_data]

                                # Add metadata about the error
                                error_row = {
                                    'ORIGINAL_LINE_NUMBER': bad_line['line_num'],
                                    'ERROR_TYPE': f"Expected {bad_line['expected']} columns, found {bad_line['found']}",
                                    'ORIGINAL_CONTENT': bad_line['content']
                                }

                                # Add the parsed data with column names
                                for i, col_name in enumerate(expected_columns):
                                    error_row[col_name] = line_parts[i] if i < len(line_parts) else ''

                                error_data.append(error_row)

                            # Save error data to CSV
                            error_df = pd.DataFrame(error_data)
                            error_df.to_csv(error_file_path, index=False)
                            print(f"✅ Error file saved with {len(error_data)} problematic rows")

                        # Now read with bad line skipping
                        df = pd.read_csv(input_file, dtype=str, on_bad_lines='skip')
                        parsing_method = "skip bad lines"
                        print("✓ Successfully parsed by skipping bad lines")

                        if bad_lines:
                            print(f"📊 RESULT: Skipped {len(bad_lines)} problematic lines")
                            print(f"   Clean data: {len(df)} rows")
                            print(f"   Bad data saved to: {os.path.basename(error_file_path)}")

                        # Verify column structure is preserved
                        if list(df.columns) != expected_columns:
                            print("⚠ Warning: Column structure may have changed")
                            print(f"Original: {expected_columns}")
                            print(f"Current:  {list(df.columns)}")

                    except Exception as e3:
                        print(f"Skip bad lines failed: {e3}")
                        print("❌ All CSV parsing methods failed!")
                        return

            if df is None:
                print("❌ Could not parse CSV file with any method")
                return

            print(f"Final parsing method used: {parsing_method}")

        elif ext == ".xlsx":
            df = pd.read_excel(input_file, dtype=str)
        else:
            print(f"Unsupported file type: {ext}")
            return

        # Debug information
        print(f"Input file: {input_file}")
        print(f"Output directory: {output_dir}")
        print(f"File extension: {ext}")
        print(f"DataFrame shape: {df.shape}")
        print(f"DataFrame columns: {list(df.columns)}")

        if df.empty:
            print("ERROR: DataFrame is empty! No data to split.")
            return

        # Column integrity check
        print(f"Column count: {len(df.columns)}")
        print(f"Sample of first row data: {df.iloc[0].tolist()[:5] if len(df) > 0 else 'No data'}")

        # Check for unnamed columns (often indicates parsing issues)
        unnamed_cols = [col for col in df.columns if 'Unnamed:' in str(col)]
        if unnamed_cols:
            print(f"⚠ Warning: Found {len(unnamed_cols)} unnamed columns: {unnamed_cols}")
            print("This might indicate column misalignment during parsing.")

        os.makedirs(output_dir, exist_ok=True)
        print(f"Created/verified output directory: {output_dir}")

        total_rows = len(df)
        num_chunks = -(-total_rows // chunk_size)  # ceiling division

        print(f"Total rows: {total_rows}")
        print(f"Chunk size: {chunk_size}")
        print(f"Number of chunks to create: {num_chunks}")

        for i in range(num_chunks):
            start_row = i * chunk_size
            end_row = (i + 1) * chunk_size
            chunk = df.iloc[start_row:end_row]

            out_name = os.path.join(
                output_dir,
                f"{os.path.splitext(os.path.basename(input_file))[0]}_{i+1:06}{ext}",
            )

            print(f"Creating chunk {i + 1}/{num_chunks}: rows {start_row}-{end_row-1} -> {out_name}")
            print(f"Chunk shape: {chunk.shape}")

            try:
                if ext == ".csv":
                    chunk.to_csv(out_name, index=False)
                else:
                    chunk.to_excel(out_name, index=False, engine="openpyxl")

                # Verify file was created
                if os.path.exists(out_name):
                    file_size = os.path.getsize(out_name)
                    print(f"✓ Chunk {i + 1} saved successfully: {out_name} ({file_size} bytes)")
                else:
                    print(f"❌ ERROR: File was not created: {out_name}")

            except Exception as chunk_error:
                print(f"❌ ERROR saving chunk {i + 1}: {chunk_error}")

    except Exception as e:
        print(f"Error during splitting: {e}")

def merge_files(input_files: List[str], output_file: str) -> None:
    """Concatenate many CSV / XLSX files into one."""
    try:
        if not input_files:
            print("No files selected for merging.")
            return

        ext = os.path.splitext(input_files[0])[1].lower()
        if ext not in {".csv", ".xlsx"}:
            print("Unsupported file format. Please select CSV or Excel only.")
            return

        frames = []
        for file in input_files:
            if file.endswith(".csv"):
                df = pd.read_csv(file, dtype=str)
            elif file.endswith(".xlsx"):
                df = pd.read_excel(file, dtype=str)
            else:
                print(f"Skipping unsupported file: {file}")
                continue
            frames.append(df)

        merged_df = pd.concat(frames, ignore_index=True)

        if ext == ".csv":
            merged_df.to_csv(output_file, index=False)
        else:
            merged_df.to_excel(output_file, index=False, engine="openpyxl")

        print(f"Files successfully merged into {output_file}")

    except Exception as e:
        print(f"Error during merging: {e}")

def compare_files(file1: str, file2: str, method: str = "auto", manual_columns: List = None, \
                 case_sensitive: bool = False, ignore_whitespace: bool = True) -> None:
    """
    Compare two CSV/Excel files to find matching data.
    
    Args:
        file1: Path to first file
        file2: Path to second file  
        method: "auto" for auto-detection or "manual" for manual column selection
        manual_columns: List of column names to compare (used when method="manual")
        case_sensitive: Whether to perform case-sensitive comparison
        ignore_whitespace: Whether to ignore leading/trailing whitespace
    """
    try:
        print(f"🔍 Comparing files:")
        print(f"   File 1: {os.path.basename(file1)}")
        print(f"   File 2: {os.path.basename(file2)}")
        print(f"   Method: {method}")
        print(f"   Case sensitive: {case_sensitive}")
        print(f"   Ignore whitespace: {ignore_whitespace}")
        print()

        # Load the files
        print("📂 Loading files...")
        
        # Load file 1
        ext1 = os.path.splitext(file1)[1].lower()
        if ext1 == ".csv":
            try:
                df1 = pd.read_csv(file1, dtype=str)
            except pd.errors.ParserError as e:
                print(f"Standard CSV parsing failed for file 1: {e}")
                try:
                    df1 = pd.read_csv(file1, dtype=str, engine='python')
                    print("✓ File 1 loaded using python engine")
                except Exception as e2:
                    print(f"Python engine failed for file 1: {e2}")
                    try:
                        df1 = pd.read_csv(file1, dtype=str, on_bad_lines='skip')
                        print("✓ File 1 loaded by skipping bad lines")
                    except Exception as e3:
                        print(f"❌ All CSV parsing methods failed for file 1: {e3}")
                        return
        elif ext1 == ".xlsx":
            df1 = pd.read_excel(file1, dtype=str)
        else:
            print(f"❌ Unsupported file format for file 1: {ext1}")
            return

        # Load file 2
        ext2 = os.path.splitext(file2)[1].lower()
        if ext2 == ".csv":
            try:
                df2 = pd.read_csv(file2, dtype=str)
            except pd.errors.ParserError as e:
                print(f"Standard CSV parsing failed for file 2: {e}")
                try:
                    df2 = pd.read_csv(file2, dtype=str, engine='python')
                    print("✓ File 2 loaded using python engine")
                except Exception as e2:
                    print(f"Python engine failed for file 2: {e2}")
                    try:
                        df2 = pd.read_csv(file2, dtype=str, on_bad_lines='skip')
                        print("✓ File 2 loaded by skipping bad lines")
                    except Exception as e3:
                        print(f"❌ All CSV parsing methods failed for file 2: {e3}")
                        return
        elif ext2 == ".xlsx":
            df2 = pd.read_excel(file2, dtype=str)
        else:
            print(f"❌ Unsupported file format for file 2: {ext2}")
            return

        print(f"✅ File 1 loaded: {len(df1)} rows, {len(df1.columns)} columns")
        print(f"✅ File 2 loaded: {len(df2)} rows, {len(df2.columns)} columns")
        print()

        # Handle NaN values
        df1 = df1.fillna('')
        df2 = df2.fillna('')

        # Get column information
        cols1 = list(df1.columns)
        cols2 = list(df2.columns)
        
        print(f"📋 File 1 columns: {cols1}")
        print(f"📋 File 2 columns: {cols2}")
        print()

        # Determine comparison columns
        if method == "auto":
            print("🤖 Auto-detecting matching columns...")
            
            # Common column patterns to look for
            common_patterns = [
                ['name', 'first_name', 'firstname', 'fname'],
                ['surname', 'last_name', 'lastname', 'lname', 'family_name'],
                ['email', 'email_address', 'e_mail'],
                ['phone', 'telephone', 'mobile', 'cell', 'phone_number'],
                ['number', 'id', 'customer_id', 'user_id', 'account_number'],
                ['address', 'street', 'location'],
                ['city', 'town'],
                ['postcode', 'zip', 'postal_code', 'zip_code'],
                ['country'],
                ['company', 'organization', 'employer']
            ]
            
            # Find matching columns
            comparison_columns = []
            
            # First, find exact matches (case-insensitive)
            cols1_lower = [col.lower() for col in cols1]
            cols2_lower = [col.lower() for col in cols2]
            
            for col1, col1_lower in zip(cols1, cols1_lower):
                for col2, col2_lower in zip(cols2, cols2_lower):
                    if col1_lower == col2_lower:
                        comparison_columns.append((col1, col2))
                        break
            
            # Then, find pattern matches
            for pattern in common_patterns:
                for pattern_name in pattern:
                    col1_match = None
                    col2_match = None
                    
                    # Find in file 1
                    for col in cols1_lower:
                        if pattern_name in col:
                            col1_match = cols1[cols1_lower.index(col)]
                            break
                    
                    # Find in file 2
                    for col in cols2_lower:
                        if pattern_name in col:
                            col2_match = cols2[cols2_lower.index(col)]
                            break
                    
                    # If found in both files and not already added
                    if col1_match and col2_match:
                        pair = (col1_match, col2_match)
                        if pair not in comparison_columns:
                            comparison_columns.append(pair)
                        break
            
            if comparison_columns:
                print(f"✅ Found {len(comparison_columns)} matching column pairs:")
                for col1, col2 in comparison_columns:
                    print(f"   • '{col1}' ↔ '{col2}'")
            else:
                print("⚠️ No matching columns found automatically.")
                print("💡 Try manual mode and specify column names to compare.")
                return
                
        else:  # manual mode
            print("👤 Using manual column selection...")
            
            if not manual_columns:
                print("❌ No manual columns specified!")
                return
            
            comparison_columns = []
            missing_cols = []
            
            for col_name in manual_columns:
                col1_match = None
                col2_match = None
                
                # Find exact match (case-insensitive)
                for col in cols1:
                    if col.lower() == col_name.lower():
                        col1_match = col
                        break
                
                for col in cols2:
                    if col.lower() == col_name.lower():
                        col2_match = col
                        break
                
                if col1_match and col2_match:
                    comparison_columns.append((col1_match, col2_match))
                    print(f"✅ Found column pair: '{col1_match}' ↔ '{col2_match}'")
                else:
                    missing_info = []
                    if not col1_match:
                        missing_info.append(f"File 1")
                    if not col2_match:
                        missing_info.append(f"File 2")
                    missing_cols.append(f"'{col_name}' (missing in {', '.join(missing_info)})")
            
            if missing_cols:
                print(f"⚠️ Could not find these columns: {', '.join(missing_cols)}")
            
            if not comparison_columns:
                print("❌ No valid column pairs found for comparison!")
                return

        print()
        print("🔍 Starting comparison...")
        
        # Prepare data for comparison
        def prepare_value(value):
            if pd.isna(value) or value is None:
                return ""
            value = str(value)
            if ignore_whitespace:
                value = value.strip()
            if not case_sensitive:
                value = value.lower()
            return value

        # Create comparison keys for each row
        def create_comparison_key(row, columns):
            return tuple(prepare_value(row[col]) for col in columns)

        # Extract comparison columns
        file1_cols = [pair[0] for pair in comparison_columns]
        file2_cols = [pair[1] for pair in comparison_columns]
        
        print(f"📊 Comparing based on columns: {file1_cols} vs {file2_cols}")
        
        # Create comparison keys
        file1_keys = {}
        file2_keys = {}
        
        for idx, row in df1.iterrows():
            key = create_comparison_key(row, file1_cols)
            if key not in file1_keys:
                file1_keys[key] = []
            file1_keys[key].append(idx)
        
        for idx, row in df2.iterrows():
            key = create_comparison_key(row, file2_cols)
            if key not in file2_keys:
                file2_keys[key] = []
            file2_keys[key].append(idx)
        
        # Find matches
        matches = []
        file1_matched_indices = set()
        file2_matched_indices = set()
        
        for key in file1_keys:
            if key in file2_keys:
                # Skip empty keys (all fields empty)
                if all(not field for field in key):
                    continue
                    
                for idx1 in file1_keys[key]:
                    for idx2 in file2_keys[key]:
                        matches.append((idx1, idx2, key))
                        file1_matched_indices.add(idx1)
                        file2_matched_indices.add(idx2)
        
        # Calculate statistics
        total_file1 = len(df1)
        total_file2 = len(df2)
        matched_file1 = len(file1_matched_indices)
        matched_file2 = len(file2_matched_indices)
        unique_matches = len(matches)
        
        print()
        print("📈 COMPARISON RESULTS:")
        print("=" * 50)
        print(f"📁 File 1: {total_file1} total rows")
        print(f"📁 File 2: {total_file2} total rows")
        print()
        print(f"✅ Matches found: {unique_matches}")
        print(f"📊 File 1 matched: {matched_file1}/{total_file1} ({matched_file1/total_file1*100:.1f}%)")
        print(f"📊 File 2 matched: {matched_file2}/{total_file2} ({matched_file2/total_file2*100:.1f}%)")
        print()
        
        # Show unmatched counts
        unmatched_file1 = total_file1 - matched_file1
        unmatched_file2 = total_file2 - matched_file2
        print(f"❌ File 1 unmatched: {unmatched_file1}")
        print(f"❌ File 2 unmatched: {unmatched_file2}")
        print()
        
        # Show sample matches
        if matches:
            print("🔍 SAMPLE MATCHES (first 10):")
            print("-" * 50)
            
            for i, (idx1, idx2, key) in enumerate(matches[:10]):
                print(f"Match {i+1}:")
                print(f"  File 1 (row {idx1+2}): {dict(zip(file1_cols, [df1.iloc[idx1][col] for col in file1_cols]))}")
                print(f"  File 2 (row {idx2+2}): {dict(zip(file2_cols, [df2.iloc[idx2][col] for col in file2_cols]))}")
                print()
            
            if len(matches) > 10:
                print(f"... and {len(matches) - 10} more matches")
                print()

            # Prompt to save matches to file
            try:
                import tkinter as tk
                from tkinter import filedialog
                root = tk.Tk()
                root.withdraw()
                save_path = filedialog.asksaveasfilename(
                    parent=root,
                    title="Save matching records as...",
                    defaultextension=".csv",
                    filetypes=[("CSV files", "*.csv"), ("Excel files", "*.xlsx"), ("All files", "*.*")]
                )
                root.destroy()
                if save_path:
                    # Save matches from file 1 (with all columns)
                    matched_indices = [idx1 for idx1, idx2, key in matches]
                    matched_df = df1.iloc[matched_indices].copy()
                    if save_path.lower().endswith('.xlsx'):
                        matched_df.to_excel(save_path, index=False, engine="openpyxl")
                    else:
                        matched_df.to_csv(save_path, index=False)
                    print(f"💾 Matching records saved to: {save_path}")
            except Exception as save_err:
                print(f"⚠️ Could not save matching records: {save_err}")
        
        # Show sample unmatched records
        unmatched_file1_indices = set(range(len(df1))) - file1_matched_indices
        unmatched_file2_indices = set(range(len(df2))) - file2_matched_indices
        
        if unmatched_file1_indices:
            print("❌ SAMPLE UNMATCHED FROM FILE 1 (first 5):")
            print("-" * 50)
            for i, idx in enumerate(list(unmatched_file1_indices)[:5]):
                print(f"  Row {idx+2}: {dict(zip(file1_cols, [df1.iloc[idx][col] for col in file1_cols]))}")
            if len(unmatched_file1_indices) > 5:
                print(f"  ... and {len(unmatched_file1_indices) - 5} more unmatched records")
            print()
        
        if unmatched_file2_indices:
            print("❌ SAMPLE UNMATCHED FROM FILE 2 (first 5):")
            print("-" * 50)
            for i, idx in enumerate(list(unmatched_file2_indices)[:5]):
                print(f"  Row {idx+2}: {dict(zip(file2_cols, [df2.iloc[idx][col] for col in file2_cols]))}")
            if len(unmatched_file2_indices) > 5:
                print(f"  ... and {len(unmatched_file2_indices) - 5} more unmatched records")
            print()
        
        # Summary
        print("📋 SUMMARY:")
        print("=" * 50)
        if unique_matches > 0:
            print(f"✅ Found {unique_matches} matching records between the files")
            print(f"📊 Match rate: File 1: {matched_file1/total_file1*100:.1f}%, File 2: {matched_file2/total_file2*100:.1f}%")
        else:
            print("❌ No matching records found between the files")
            print("💡 Suggestions:")
            print("   • Check if the data formats are consistent")
            print("   • Try different column combinations")
            print("   • Check for typos or extra spaces in the data")
            print("   • Consider case sensitivity settings")
        
        print()
        print("🎉 Comparison completed!")

    except Exception as e:
        print(f"❌ Error during comparison: {e}")
        import traceback
        traceback.print_exc()

# ───────────────────────────── CLI wrapper ───────────────────────────────
def main() -> None:
    print("Welcome! Please select an operation:")
    print("1. Split a file")
    print("2. Merge files")
    choice = input("Enter your choice (1 or 2): ").strip()

    if choice == "1":  # ��─ Split ─────────────────────────────────────────
        input_file = select_file()
        if not input_file:
            print("No file selected. Exiting.")
            return

        output_dir = select_directory()
        if not output_dir:
            print("No directory selected. Exiting.")
            return

        chunk_size_str = input("Rows per chunk: ").strip()
        if not chunk_size_str.isdigit():
            print("Invalid chunk size. Exiting.")
            return

        split_file(input_file, output_dir, int(chunk_size_str))

    elif choice == "2":  # ── Merge ───────────────────────────────────────
        input_files = select_files()
        if not input_files:
            print("No files selected. Exiting.")
            return

        root = tk.Tk()
        root.withdraw()
        output_file = filedialog.asksaveasfilename(
            parent=root,
            title="Save merged file as…",
            defaultextension=".csv",
            filetypes=[
                ("Excel & CSV files", ("*.xlsx", "*.csv")),
                ("Excel files", "*.xlsx"),
                ("CSV files", "*.csv"),
            ],
        )
        root.destroy()

        if not output_file:
            print("No output file selected. Exiting.")
            return

        merge_files(input_files, output_file)

    else:
        print("Invalid choice. Exiting.")

if __name__ == "__main__":
    main()
