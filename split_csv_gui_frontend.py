import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from split_csv_gui import split_file, merge_files, select_file, select_files, select_directory
import threading
import sys
import os
from io import StringIO

class CSVSplitterGUI:
    def __init__(self):
        self.window = tk.Tk()
        self.window.title("🚀 CSV/Excel Splitter & Merger Pro")
        self.window.geometry("900x700")
        self.window.resizable(True, True)

        # Modern color scheme
        self.colors = {
            'bg': '#2b2b2b',
            'fg': '#ffffff',
            'accent': '#4CAF50',
            'warning': '#FF9800',
            'error': '#F44336',
            'info': '#2196F3',
            'card': '#3c3c3c'
        }

        # Configure window style
        self.window.configure(bg=self.colors['bg'])

        # Configure ttk styles
        self.setup_styles()

        # Create main container with padding
        main_container = ttk.Frame(self.window, style='Main.TFrame')
        main_container.pack(expand=True, fill='both', padx=20, pady=20)

        # Title
        title_label = ttk.Label(main_container, text="🚀 CSV/Excel Splitter & Merger Pro",
                               style='Title.TLabel')
        title_label.pack(pady=(0, 20))

        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_container, style='Modern.TNotebook')
        self.notebook.pack(expand=True, fill='both')

        # Create Split and Merge tabs
        self.split_tab = ttk.Frame(self.notebook, style='Tab.TFrame')
        self.merge_tab = ttk.Frame(self.notebook, style='Tab.TFrame')
        self.notebook.add(self.split_tab, text='🔪 Split File')
        self.notebook.add(self.merge_tab, text='🔗 Merge Files')

        self.setup_split_tab()
        self.setup_merge_tab()

    def setup_styles(self):
        """Configure modern ttk styles"""
        style = ttk.Style()

        # Main frame style
        style.configure('Main.TFrame', background=self.colors['bg'])
        style.configure('Tab.TFrame', background=self.colors['bg'])
        style.configure('Card.TFrame', background=self.colors['card'], relief='raised', borderwidth=1)

        # Title style
        style.configure('Title.TLabel', background=self.colors['bg'], foreground=self.colors['accent'],
                       font=('Segoe UI', 18, 'bold'))

        # Modern label styles
        style.configure('Modern.TLabel', background=self.colors['bg'], foreground=self.colors['fg'],
                       font=('Segoe UI', 10))
        style.configure('Header.TLabel', background=self.colors['bg'], foreground=self.colors['accent'],
                       font=('Segoe UI', 12, 'bold'))

        # Button styles
        style.configure('Modern.TButton', font=('Segoe UI', 10, 'bold'))
        style.configure('Action.TButton', font=('Segoe UI', 12, 'bold'))

        # Entry styles
        style.configure('Modern.TEntry', font=('Segoe UI', 10))

        # Notebook styles
        style.configure('Modern.TNotebook', background=self.colors['bg'])
        style.configure('Modern.TNotebook.Tab', padding=[20, 10], font=('Segoe UI', 10, 'bold'))

    def setup_split_tab(self):
        # Create scrollable frame
        canvas = tk.Canvas(self.split_tab, bg=self.colors['bg'], highlightthickness=0)
        scrollbar = ttk.Scrollbar(self.split_tab, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas, style='Tab.TFrame')

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Input File Card
        input_card = ttk.Frame(scrollable_frame, style='Card.TFrame')
        input_card.pack(fill='x', padx=10, pady=10)

        ttk.Label(input_card, text="📁 Input File", style='Header.TLabel').pack(anchor='w', padx=15, pady=(15, 5))

        file_frame = ttk.Frame(input_card, style='Card.TFrame')
        file_frame.pack(fill='x', padx=15, pady=(0, 15))

        self.input_file_var = tk.StringVar()
        self.input_file_var.trace('w', self.on_file_selected)

        ttk.Entry(file_frame, textvariable=self.input_file_var, width=60, style='Modern.TEntry').pack(side='left', fill='x', expand=True, padx=(0, 10))
        ttk.Button(file_frame, text="📂 Browse", command=self.browse_input_file, style='Modern.TButton').pack(side='right')

        # File Info Card (initially hidden)
        self.file_info_card = ttk.Frame(scrollable_frame, style='Card.TFrame')
        self.file_info_text = scrolledtext.ScrolledText(self.file_info_card, height=8, width=80,
                                                       font=('Consolas', 9), bg='#1e1e1e', fg='#ffffff',
                                                       insertbackground='white')

        # Output Directory Card
        output_card = ttk.Frame(scrollable_frame, style='Card.TFrame')
        output_card.pack(fill='x', padx=10, pady=10)

        ttk.Label(output_card, text="📂 Output Directory", style='Header.TLabel').pack(anchor='w', padx=15, pady=(15, 5))

        dir_frame = ttk.Frame(output_card, style='Card.TFrame')
        dir_frame.pack(fill='x', padx=15, pady=(0, 15))

        self.output_dir_var = tk.StringVar()
        ttk.Entry(dir_frame, textvariable=self.output_dir_var, width=60, style='Modern.TEntry').pack(side='left', fill='x', expand=True, padx=(0, 10))
        ttk.Button(dir_frame, text="📁 Browse", command=self.browse_output_dir, style='Modern.TButton').pack(side='right')

        # Settings Card
        settings_card = ttk.Frame(scrollable_frame, style='Card.TFrame')
        settings_card.pack(fill='x', padx=10, pady=10)

        ttk.Label(settings_card, text="⚙️ Split Settings", style='Header.TLabel').pack(anchor='w', padx=15, pady=(15, 5))

        settings_frame = ttk.Frame(settings_card, style='Card.TFrame')
        settings_frame.pack(fill='x', padx=15, pady=(0, 15))

        ttk.Label(settings_frame, text="Rows per chunk:", style='Modern.TLabel').pack(side='left', padx=(0, 10))
        self.chunk_size_var = tk.StringVar(value="11111")
        chunk_entry = ttk.Entry(settings_frame, textvariable=self.chunk_size_var, width=15, style='Modern.TEntry')
        chunk_entry.pack(side='left', padx=(0, 20))

        # Calculation display
        self.calc_label = ttk.Label(settings_frame, text="", style='Modern.TLabel')
        self.calc_label.pack(side='left')
        self.chunk_size_var.trace('w', self.update_calculations)

        # Action Button
        action_frame = ttk.Frame(scrollable_frame, style='Tab.TFrame')
        action_frame.pack(fill='x', padx=10, pady=20)

        self.split_button = ttk.Button(action_frame, text="🚀 Split File", command=self.split_action,
                                      style='Action.TButton', state='disabled')
        self.split_button.pack(pady=10)

    def setup_merge_tab(self):
        # File selection
        ttk.Label(self.merge_tab, text="Input Files:").pack(pady=5)
        self.files_listbox = tk.Listbox(self.merge_tab, width=50, height=8)
        self.files_listbox.pack(pady=5)
        ttk.Button(self.merge_tab, text="Select Files", command=self.browse_merge_files).pack(pady=5)

        # Merge button
        ttk.Button(self.merge_tab, text="Merge Files", command=self.merge_action).pack(pady=20)

    def browse_input_file(self):
        file_path = select_file()
        if file_path:
            self.input_file_var.set(file_path)

    def on_file_selected(self, *args):
        """Analyze file when selected and show diagnostics"""
        file_path = self.input_file_var.get()
        if not file_path or not os.path.exists(file_path):
            self.file_info_card.pack_forget()
            self.split_button.configure(state='disabled')
            return

        # Show file info card
        self.file_info_card.pack(fill='x', padx=10, pady=10, after=self.file_info_card.master.winfo_children()[0])

        ttk.Label(self.file_info_card, text="📊 File Analysis", style='Header.TLabel').pack(anchor='w', padx=15, pady=(15, 5))
        self.file_info_text.pack(fill='both', expand=True, padx=15, pady=(0, 15))

        # Analyze file in background
        threading.Thread(target=self.analyze_file, args=(file_path,), daemon=True).start()

        self.split_button.configure(state='normal')
        self.update_calculations()

    def analyze_file(self, file_path):
        """Analyze CSV file and display diagnostics"""
        self.file_info_text.delete(1.0, tk.END)
        self.file_info_text.insert(tk.END, "🔍 Analyzing file...\n")
        self.file_info_text.update()

        try:
            # Get file size
            file_size = os.path.getsize(file_path)
            size_mb = file_size / (1024 * 1024)

            self.file_info_text.insert(tk.END, f"📁 File: {os.path.basename(file_path)}\n")
            self.file_info_text.insert(tk.END, f"💾 Size: {size_mb:.2f} MB\n")
            self.file_info_text.insert(tk.END, f"📍 Path: {file_path}\n\n")

            # Quick CSV analysis
            if file_path.lower().endswith('.csv'):
                self.file_info_text.insert(tk.END, "🔍 CSV Analysis:\n")

                # Count lines quickly
                with open(file_path, 'r', encoding='utf-8') as f:
                    line_count = sum(1 for _ in f)

                self.file_info_text.insert(tk.END, f"📊 Total lines: {line_count:,}\n")

                # Check first few lines for structure
                with open(file_path, 'r', encoding='utf-8') as f:
                    first_line = f.readline().strip()
                    expected_cols = len(first_line.split(','))

                self.file_info_text.insert(tk.END, f"📋 Expected columns: {expected_cols}\n")
                self.file_info_text.insert(tk.END, f"🏷️ Headers: {first_line[:100]}{'...' if len(first_line) > 100 else ''}\n\n")

                # Quick scan for issues
                self.file_info_text.insert(tk.END, "🔍 Quick issue scan...\n")
                issues_found = 0

                with open(file_path, 'r', encoding='utf-8') as f:
                    for i, line in enumerate(f, 1):
                        if i == 1:  # Skip header
                            continue
                        if i > 1000:  # Limit scan
                            break

                        actual_cols = len(line.strip().split(','))
                        if actual_cols != expected_cols:
                            issues_found += 1
                            if issues_found <= 3:  # Show first 3 issues
                                self.file_info_text.insert(tk.END, f"⚠️ Line {i}: {expected_cols} → {actual_cols} columns\n")

                if issues_found == 0:
                    self.file_info_text.insert(tk.END, "✅ No obvious issues found in first 1000 lines\n")
                elif issues_found > 3:
                    self.file_info_text.insert(tk.END, f"⚠️ Found {issues_found} potential issues (showing first 3)\n")

                self.file_info_text.insert(tk.END, "\n🚀 Ready to split!\n")

            else:
                self.file_info_text.insert(tk.END, "📊 Excel file detected - ready to process!\n")

        except Exception as e:
            self.file_info_text.insert(tk.END, f"❌ Error analyzing file: {str(e)}\n")

        self.file_info_text.see(tk.END)

    def update_calculations(self, *args):
        """Update file split calculations"""
        try:
            chunk_size = int(self.chunk_size_var.get())
            file_path = self.input_file_var.get()

            if file_path and os.path.exists(file_path) and chunk_size > 0:
                # Estimate total rows (quick for display)
                if file_path.lower().endswith('.csv'):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        total_rows = sum(1 for _ in f) - 1  # Subtract header
                else:
                    total_rows = "Unknown"

                if isinstance(total_rows, int):
                    num_files = -(-total_rows // chunk_size)  # Ceiling division
                    last_file_rows = total_rows % chunk_size or chunk_size

                    calc_text = f"📊 {total_rows:,} rows → {num_files} files ({chunk_size:,} + {last_file_rows:,} in last)"
                else:
                    calc_text = f"📊 Will create files of {chunk_size:,} rows each"

                self.calc_label.configure(text=calc_text)
            else:
                self.calc_label.configure(text="")
        except ValueError:
            self.calc_label.configure(text="⚠️ Invalid chunk size")

    def browse_output_dir(self):
        dir_path = select_directory()
        if dir_path:
            self.output_dir_var.set(dir_path)

    def browse_merge_files(self):
        files = select_files()
        if files:
            self.files_listbox.delete(0, tk.END)
            for file in files:
                self.files_listbox.insert(tk.END, file)

    def split_action(self):
        input_file = self.input_file_var.get()
        output_dir = self.output_dir_var.get()
        chunk_size = self.chunk_size_var.get()

        if not input_file or not output_dir or not chunk_size:
            messagebox.showerror("Error", "Please fill in all fields")
            return

        try:
            chunk_size = int(chunk_size)
            if chunk_size <= 0:
                raise ValueError("Chunk size must be positive")
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid positive number for chunk size")
            return

        # Create a progress window
        progress_window = tk.Toplevel(self.window)
        progress_window.title("Splitting File...")
        progress_window.geometry("400x200")
        progress_window.transient(self.window)
        progress_window.grab_set()

        # Progress label
        progress_label = ttk.Label(progress_window, text="Processing file...")
        progress_label.pack(pady=10)

        # Progress bar
        progress_bar = ttk.Progressbar(progress_window, mode='determinate', maximum=100)
        progress_bar.pack(pady=10, padx=20, fill='x')
        progress_bar['value'] = 0

        # Text area for detailed messages
        text_area = scrolledtext.ScrolledText(progress_window, height=6, width=50)
        text_area.pack(pady=10, padx=10, fill='both', expand=True)

        def update_progress(message, progress_value=None):
            text_area.insert(tk.END, message + "\n")
            text_area.see(tk.END)
            if progress_value is not None:
                progress_bar['value'] = progress_value
            progress_window.update()

        try:
            # Capture print statements from split_file function
            old_stdout = sys.stdout
            sys.stdout = captured_output = StringIO()

            update_progress("Starting file split operation...", 10)
            update_progress(f"Input: {input_file}")
            update_progress(f"Output: {output_dir}")
            update_progress(f"Chunk size: {chunk_size}")

            update_progress("Analyzing and processing file...", 25)

            # Call split function
            split_file(input_file, output_dir, chunk_size)

            # Get captured output
            output = captured_output.getvalue()
            sys.stdout = old_stdout

            update_progress("Split function completed, checking output...", 90)

            # Show any messages from the split operation
            if output:
                update_progress("=== Split Function Output ===")
                for line in output.strip().split('\n'):
                    if line.strip():
                        update_progress(line)
            else:
                update_progress("⚠️ No output from split function!")

            # Check if files were actually created
            if os.path.exists(output_dir):
                created_files = [f for f in os.listdir(output_dir) if f.endswith(('.csv', '.xlsx'))]
                if created_files:
                    update_progress(f"✅ Created {len(created_files)} files:")
                    for f in created_files[:3]:  # Show first 3
                        update_progress(f"   - {f}")
                    if len(created_files) > 3:
                        update_progress(f"   ... and {len(created_files) - 3} more")
                else:
                    update_progress("❌ No files were created in output directory!")
            else:
                update_progress("❌ Output directory doesn't exist!")

            progress_bar.stop()

            # Show completion status
            if os.path.exists(output_dir):
                created_files = [f for f in os.listdir(output_dir) if f.endswith(('.csv', '.xlsx'))]
                if len(created_files) >= 2:  # At least main files were created
                    update_progress("🎉 OPERATION COMPLETED SUCCESSFULLY!", 100)
                    update_progress("All files have been created. You can close this window.")
                else:
                    update_progress("⚠️ Operation completed but fewer files than expected were created.", 100)

            # Release the window grab and show close button
            progress_window.grab_release()
            close_frame = ttk.Frame(progress_window)
            close_frame.pack(pady=10)
            ttk.Button(close_frame, text="✅ Close", command=progress_window.destroy).pack()

        except Exception as e:
            sys.stdout = old_stdout
            progress_bar.stop()
            progress_window.destroy()

            error_msg = str(e)
            if "Error tokenizing data" in error_msg:
                detailed_msg = (
                    f"CSV parsing error detected:\n\n{error_msg}\n\n"
                    "This usually means your CSV file has formatting issues like:\n"
                    "• Unescaped commas in text fields\n"
                    "• Missing quotes around fields\n"
                    "• Inconsistent number of columns\n\n"
                    "The updated splitter should handle this automatically. "
                    "If it still fails, try cleaning your CSV file first."
                )
                messagebox.showerror("CSV Format Error", detailed_msg)
            else:
                messagebox.showerror("Error", f"Error splitting file: {error_msg}")

    def merge_action(self):
        files = list(self.files_listbox.get(0, tk.END))
        if not files:
            messagebox.showerror("Error", "Please select files to merge")
            return

        # Use proper save dialog for output file
        from tkinter import filedialog
        root = tk.Tk()
        root.withdraw()
        output_file = filedialog.asksaveasfilename(
            parent=root,
            title="Save merged file as...",
            defaultextension=".csv",
            filetypes=[
                ("CSV files", "*.csv"),
                ("Excel files", "*.xlsx"),
                ("All files", "*.*")
            ]
        )
        root.destroy()

        if not output_file:
            return

        # Create progress window for merge operation
        progress_window = tk.Toplevel(self.window)
        progress_window.title("Merging Files...")
        progress_window.geometry("500x300")
        progress_window.transient(self.window)
        progress_window.grab_set()

        # Progress label
        progress_label = ttk.Label(progress_window, text="Merging files...")
        progress_label.pack(pady=10)

        # Progress bar
        progress_bar = ttk.Progressbar(progress_window, mode='determinate', maximum=100)
        progress_bar.pack(pady=10, padx=20, fill='x')
        progress_bar['value'] = 0

        # Text area for detailed messages
        text_area = scrolledtext.ScrolledText(progress_window, height=8, width=60)
        text_area.pack(pady=10, padx=10, fill='both', expand=True)

        def update_progress(message, progress_value=None):
            text_area.insert(tk.END, message + "\n")
            text_area.see(tk.END)
            if progress_value is not None:
                progress_bar['value'] = progress_value
            progress_window.update()

        try:
            # Capture print statements from merge_files function
            old_stdout = sys.stdout
            sys.stdout = captured_output = StringIO()

            update_progress("Starting file merge operation...", 10)
            update_progress(f"Files to merge: {len(files)}")
            for i, f in enumerate(files[:3]):  # Show first 3
                update_progress(f"  - {os.path.basename(f)}")
            if len(files) > 3:
                update_progress(f"  ... and {len(files) - 3} more files")
            update_progress(f"Output file: {output_file}")

            update_progress("Processing and merging files...", 25)

            # Call merge function
            merge_files(files, output_file)

            # Get captured output
            output = captured_output.getvalue()
            sys.stdout = old_stdout

            update_progress("Merge function completed, checking output...", 90)

            # Show any messages from the merge operation
            if output:
                update_progress("=== Merge Function Output ===")
                for line in output.strip().split('\n'):
                    if line.strip():
                        update_progress(line)
            else:
                update_progress("⚠️ No output from merge function!")

            # Check if output file was created
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                size_mb = file_size / (1024 * 1024)
                update_progress(f"✅ Merged file created: {os.path.basename(output_file)}")
                update_progress(f"📊 File size: {size_mb:.2f} MB")
                update_progress("🎉 MERGE COMPLETED SUCCESSFULLY!", 100)
                update_progress("Merged file has been created. You can close this window.")
            else:
                update_progress("❌ Merged file was not created!", 100)

            # Release the window grab and show close button
            progress_window.grab_release()
            close_frame = ttk.Frame(progress_window)
            close_frame.pack(pady=10)
            ttk.Button(close_frame, text="✅ Close", command=progress_window.destroy).pack()

        except Exception as e:
            sys.stdout = old_stdout
            progress_bar['value'] = 100
            progress_window.grab_release()

            error_msg = str(e)
            update_progress(f"❌ Error during merge: {error_msg}")

            close_frame = ttk.Frame(progress_window)
            close_frame.pack(pady=10)
            ttk.Button(close_frame, text="Close", command=progress_window.destroy).pack()

    def run(self):
        self.window.mainloop()

if __name__ == "__main__":
    app = CSVSplitterGUI()
    app.run()